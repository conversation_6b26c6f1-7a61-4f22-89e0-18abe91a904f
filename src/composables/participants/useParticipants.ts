import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useParticipantGraph } from '@/api/graphHooks/useParticipantGraph'
import { Participant, EmploymentInfo } from '@/types/participant.types'
import { SalaryEntry } from '@/gql/graphql'
import { transformObjectToArray } from '@/utils/transformers'
import { usePensionStore } from '@/stores/pension/pensionStore'
import { toISOString } from '@/utils/dateUtils'

export const useParticipants = () => {
  const {
    state: {
      participantsList,
      loadingParticipants,
      singleParticipant,
      loadingParticipant,
      creatingParticipant
    },
    actions: {
      refetchParticipants,
      refetchSingleParticipant
    },
    mutations: {
      createParticipantMutation
    }
  } = useParticipantGraph()

  const pensionStore = usePensionStore()

  const formattedParticipants = computed(() => {
    return participantsList.value.map((participant: Participant) => {
      const { personalInfo } = participant
      const birthDate = new Date(personalInfo.birthYear, personalInfo.birthMonth - 1, personalInfo.birthDay).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })
      const fullName = `${personalInfo.firstName} ${personalInfo.lastName}`
      return {
        lastName: participant?.personalInfo?.lastName,
        firstName: participant?.personalInfo?.firstName,
        id: participant.id,
        fullName,
        birthDate,
        status: participant?.pensionInfo?.codeDescription || participant?.status,
      }
    })
  })


  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'success'
      case 'pending':
        return 'warning'
      case 'inactive':
        return 'error'
      default:
        return 'default'
    }
  }

  const participantDetails = computed(() => {
    pensionStore.setActiveParticipant(singleParticipant.value)
    const birthYear = singleParticipant.value?.personalInfo.birthYear || '1900'
    const birthMonth = singleParticipant.value?.personalInfo.birthMonth || '01'
    const birthDay = singleParticipant.value?.personalInfo.birthDay || '01'
    const birthDate = new Date(`${birthYear}-${birthMonth}-${birthDay}`).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })

    return transformObjectToArray({ ...singleParticipant.value?.personalInfo, birthDate: birthDate })
  })

  const participantPartnerInfo = computed(() => {
    const partnerInfo = singleParticipant.value?.personalInfo?.partnerInfo.filter((info: any) => info.isCurrent === true)
    if (!partnerInfo) return []
    return transformObjectToArray(partnerInfo[0] || {})
  })

  const participantExPartnerInfo = computed(() => {
    const partnerInfo = singleParticipant.value?.personalInfo?.partnerInfo.filter((info: any) => info.isCurrent === false)
    if (!partnerInfo) return []
    return partnerInfo.map((info: any) => {
      return transformObjectToArray(info)
    });
  })

  const participantChildrenInfo = computed(() => {
    const children = singleParticipant.value?.personalInfo?.children || [];
    return children.map((child: any) => transformObjectToArray(child));
  });

  const participantAddressInfo = computed(() => {
    const address = singleParticipant.value?.personalInfo?.address || {};
    return transformObjectToArray(address);
  });

  const participantEmploymentInfo = computed<EmploymentInfo>(() => {
    return singleParticipant.value?.employmentInfo || {} as EmploymentInfo;
  });

  //add for participant pensionInfo
  const participantPensionInfo = computed(() => {
    return singleParticipant.value?.pensionInfo || {};
  });

  const participantSalaryEntries = computed(() => {
    return singleParticipant.value?.employmentInfo?.salaryEntries || [];
  });

  const normalizedParticipantSalaryEntries = computed(() => {
    const salaryEntries = singleParticipant.value?.employmentInfo?.salaryEntries || [];
    const startDate = singleParticipant.value?.employmentInfo?.startDate;

    const result: Record<string, any> = {};
    salaryEntries.forEach((entry) => {
      if (entry.year) {
        result[entry.year.toString()] = {
          ...entry,
          startDate: startDate
        };
      }
    });
    return result;
  });

  const availableStatuses = ['Active', 'Pending', 'Inactive']

  const route = useRoute()

  const getYearInReview = (editable: boolean): number => {
    const paramsYear = computed(() => {
      return parseInt(route.params.year as string)
    })
    return editable ? paramsYear.value : paramsYear.value - 1
  }

  const error = ref<string | null>(null)

  const createParticipant = async (participantData: any) => {
    error.value = null

    const currentUserId = computed(() => "be196c0f-ee7b-42a3-9163-885f649e65ef") // TODO: Get from auth store once you can set in claims

    try {
      const result = await createParticipantMutation({
        input: {
          status: 'active',
          createdBy: currentUserId.value, // TODO: Get from auth store
          updatedBy: currentUserId.value, // TODO: Get from auth store
          personalInfo: {
            firstName: participantData.personalInfo.firstName,
            lastName: participantData.personalInfo.lastName,
            email: participantData.personalInfo.email,
            phone: participantData.personalInfo.phone,
            maritalStatus: participantData.personalInfo.maritalStatus,
            birthDay: new Date(participantData.personalInfo.birthDate).getDate(),
            birthMonth: new Date(participantData.personalInfo.birthDate).getMonth() + 1,
            birthYear: new Date(participantData.personalInfo.birthDate).getFullYear(),
            address: {
              street: participantData.personalInfo.address.street,
              houseNumber: participantData.personalInfo.address.houseNumber,
              postalCode: participantData.personalInfo.address.postalCode,
              city: participantData.personalInfo.address.city,
              state: participantData.personalInfo.address.state,
              country: participantData.personalInfo.address.country
            },
            partnerInfo: (participantData.personalInfo.partners || []).map((partner: any) => ({
              firstName: partner.firstName,
              lastName: partner.lastName,
              dateOfBirth: toISOString(partner.dateOfBirth),
              startDate: toISOString(partner.startDate),
              isCurrent: partner.isCurrent,
              isDeceased: partner.isDeceased
            })),
            children: (participantData.personalInfo.children || []).map((child: any) => ({
              firstName: child.firstName,
              lastName: child.lastName,
              dateOfBirth: toISOString(child.dateOfBirth),
              isOrphan: child.isOrphan,
              isStudying: child.isStudying
            }))
          },
          employmentInfo: {
            employeeId: participantData.employmentInfo.employeeId,
            department: participantData.employmentInfo.department,
            position: participantData.employmentInfo.position,
            startDate: toISOString(participantData.employmentInfo.startDate),
            status: participantData.employmentInfo.status,
            salaryEntries: (participantData.employmentInfo.salaryEntries || []).map((entry: any) => ({
              year: entry.year,
              amount: entry.amount,
              partTimePercentage: entry.partTimePercentage
            }))
          },
          pensionInfo: {
            code: participantData.pensionInfo.code,
            codeDescription: participantData.pensionInfo.codeDescription,
            codeEffectiveDate: toISOString(participantData.pensionInfo.codeEffectiveDate)
          }
        }
      })

      if (!result?.data) {
        throw new Error('No data returned from mutation')
      }

      return result.data.createParticipant
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'An error occurred while creating the participant'
      throw err
    }
  }


  return {
    state: {
      participantAddressInfo,
      participantChildrenInfo,
      participantsList: formattedParticipants,
      loadingParticipants,
      availableStatuses,
      participantDetails,
      loadingParticipant,
      participantPartnerInfo,
      participantExPartnerInfo,
      participantSalaryEntries,
      normalizedParticipantSalaryEntries,
      participantPersonalInfo: singleParticipant.value?.personalInfo,
      participantEmploymentInfo,
      participantPensionInfo,
      creatingParticipant,
      error
    },
    actions: {
      refetchParticipants,
      refetchSingleParticipant,
      getStatusColor,
      getYearInReview,
      createParticipant
    }
  }
}