<template>
  <div class="add-participant-container">
    <div class="header-container">
      <div class="d-flex justify-space-between align-center">
        <div>
          <h1 class="text-h4 font-weight-bold">Add New Participant</h1>
          <p class="text-subtitle-1 text-grey-darken-1">Create a new pension participant</p>
        </div>
        <div class="d-flex gap-3">
          <v-btn
            color="primary"
            variant="outlined"
            prepend-icon="tabler-download"
            @click="exportTemplate"
            :disabled="loading"
          >
            Export Excel Template
          </v-btn>
          <v-btn
            color="success"
            variant="outlined"
            prepend-icon="tabler-upload"
            @click="triggerFileUpload"
            :disabled="loading"
          >
            Import New Participant
          </v-btn>
          <input
            ref="fileInput"
            type="file"
            accept=".xlsx,.xls"
            style="display: none"
            @change="handleFileUpload"
          />
        </div>
      </div>
    </div>

    <v-overlay
      :model-value="creatingParticipant"
      class="align-center justify-center"
    >
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      ></v-progress-circular>
    </v-overlay>

    <AddParticipantForm
      @submit="handleSubmit"
      @cancel="handleCancel"
      :loading="creatingParticipant"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useParticipants } from '@/composables/participants/useParticipants'
import AddParticipantForm from '@/components/participants/AddParticipantForm.vue'
import { useAppStore } from '@/stores/app/appStore'

const router = useRouter()
const {
  state: { creatingParticipant, error },
  actions: { createParticipant }
} = useParticipants()
const appStore = useAppStore()

const handleSubmit = async (formData: any) => {
  try {
    await createParticipant(formData)
    appStore.showSnack('Participant created successfully')
    router.push('/participants')
  } catch (error) {
    console.error('Error creating participant:', error)
    appStore.showSnack('Error creating participant')
  }
}

const handleCancel = () => {
  router.push('/participants')
}
</script>

<style scoped>
.add-participant-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.header-container {
  margin-bottom: 32px;
}
</style> 