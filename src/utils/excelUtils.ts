import * as XLSX from 'xlsx'

export interface ParticipantExcelData {
  // Personal Information
  firstName: string
  lastName: string
  email: string
  phone: string
  maritalStatus: string
  birthDate: string
  
  // Address
  street: string
  houseNumber: string
  postalCode: string
  city: string
  state: string
  country: string
  
  // Employment Information
  employeeId: string
  department: string
  position: string
  startDate: string
  status: string
  
  // Salary Entry
  salaryYear: number
  salaryAmount: number
  partTimePercentage: number
  
  // Pension Information
  pensionCode: number
  codeDescription: string
  codeEffectiveDate: string
  
  // Partner Information (optional)
  partnerFirstName?: string
  partnerLastName?: string
  partnerDateOfBirth?: string
  partnerStartDate?: string
  partnerIsCurrent?: boolean
  partnerIsDeceased?: boolean
  
  // Child Information (optional)
  childFirstName?: string
  childLastName?: string
  childDateOfBirth?: string
  childIsOrphan?: boolean
  childIsStudying?: boolean
}

/**
 * Generates and downloads an Excel template for participant data
 */
export const exportParticipantTemplate = () => {
  // Create template data with headers and example row
  const templateData = [
    {
      // Personal Information
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+************',
      maritalStatus: 'Married',
      birthDate: '1990-01-15',
      
      // Address
      street: 'Main Street',
      houseNumber: '123',
      postalCode: '12345',
      city: 'Oranjestad',
      state: 'Aruba',
      country: 'Aruba',
      
      // Employment Information
      employeeId: 'EMP001',
      department: 'IT Department',
      position: 'Software Developer',
      startDate: '2020-01-01',
      status: 'Active',
      
      // Salary Entry
      salaryYear: new Date().getFullYear(),
      salaryAmount: 5000,
      partTimePercentage: 100,
      
      // Pension Information
      pensionCode: 10,
      codeDescription: 'Standard Pension',
      codeEffectiveDate: '2020-01-01',
      
      // Partner Information (optional)
      partnerFirstName: 'Jane',
      partnerLastName: 'Doe',
      partnerDateOfBirth: '1992-03-20',
      partnerStartDate: '2015-06-01',
      partnerIsCurrent: true,
      partnerIsDeceased: false,
      
      // Child Information (optional)
      childFirstName: 'Little',
      childLastName: 'Doe',
      childDateOfBirth: '2018-08-10',
      childIsOrphan: false,
      childIsStudying: true
    }
  ]

  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new()
  const worksheet = XLSX.utils.json_to_sheet(templateData)

  // Add instructions sheet
  const instructions = [
    { Field: 'Instructions', Value: 'Please fill in the participant data according to the template below' },
    { Field: '', Value: '' },
    { Field: 'Required Fields', Value: 'firstName, lastName, email, employeeId, department, position, startDate, salaryYear, salaryAmount, pensionCode' },
    { Field: '', Value: '' },
    { Field: 'Date Format', Value: 'YYYY-MM-DD (e.g., 2024-01-15)' },
    { Field: 'Marital Status Options', Value: 'Single, Married, Divorced, Widowed' },
    { Field: 'Employment Status Options', Value: 'Active, Inactive, Pending' },
    { Field: 'Boolean Fields', Value: 'Use TRUE or FALSE for partnerIsCurrent, partnerIsDeceased, childIsOrphan, childIsStudying' },
    { Field: '', Value: '' },
    { Field: 'Optional Fields', Value: 'Partner and Child information can be left empty if not applicable' },
    { Field: 'Multiple Partners/Children', Value: 'For multiple partners or children, create additional rows with the same participant info' }
  ]

  const instructionsSheet = XLSX.utils.json_to_sheet(instructions)

  // Add sheets to workbook
  XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions')
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Participant Template')

  // Generate filename with current date
  const today = new Date().toISOString().split('T')[0]
  const filename = `participant_template_${today}.xlsx`

  // Download the file
  XLSX.writeFile(workbook, filename)
}

/**
 * Parses an uploaded Excel file and returns participant data
 */
export const parseParticipantExcel = (file: File): Promise<ParticipantExcelData[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        
        // Get the first sheet (assuming participant data is in the first sheet)
        const sheetName = workbook.SheetNames.find(name => 
          name.toLowerCase().includes('template') || 
          name.toLowerCase().includes('participant') ||
          workbook.SheetNames.indexOf(name) === (workbook.SheetNames.length - 1) // Last sheet if no template found
        ) || workbook.SheetNames[0]
        
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet) as ParticipantExcelData[]
        
        // Filter out empty rows and validate required fields
        const validData = jsonData.filter(row => 
          row.firstName && 
          row.lastName && 
          row.email && 
          row.employeeId
        )
        
        if (validData.length === 0) {
          throw new Error('No valid participant data found in the Excel file')
        }
        
        resolve(validData)
      } catch (error) {
        reject(new Error(`Error parsing Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`))
      }
    }
    
    reader.onerror = () => {
      reject(new Error('Error reading file'))
    }
    
    reader.readAsArrayBuffer(file)
  })
}

/**
 * Converts Excel data to form data format
 */
export const convertExcelToFormData = (excelData: ParticipantExcelData) => {
  return {
    personalInfo: {
      firstName: excelData.firstName || '',
      lastName: excelData.lastName || '',
      email: excelData.email || '',
      phone: excelData.phone || '',
      maritalStatus: excelData.maritalStatus || '',
      birthDate: excelData.birthDate || '',
      partners: excelData.partnerFirstName ? [{
        firstName: excelData.partnerFirstName,
        lastName: excelData.partnerLastName || '',
        dateOfBirth: excelData.partnerDateOfBirth || '',
        startDate: excelData.partnerStartDate || '',
        isCurrent: excelData.partnerIsCurrent || false,
        isDeceased: excelData.partnerIsDeceased || false
      }] : [],
      children: excelData.childFirstName ? [{
        firstName: excelData.childFirstName,
        lastName: excelData.childLastName || '',
        dateOfBirth: excelData.childDateOfBirth || '',
        isOrphan: excelData.childIsOrphan || false,
        isStudying: excelData.childIsStudying || false
      }] : [],
      address: {
        street: excelData.street || '',
        houseNumber: excelData.houseNumber || '',
        postalCode: excelData.postalCode || '',
        city: excelData.city || '',
        state: excelData.state || '',
        country: excelData.country || ''
      }
    },
    employmentInfo: {
      employeeId: excelData.employeeId || '',
      department: excelData.department || '',
      position: excelData.position || '',
      startDate: excelData.startDate || '',
      status: excelData.status || 'Active',
      salaryEntries: [{
        year: excelData.salaryYear || new Date().getFullYear(),
        amount: excelData.salaryAmount || null,
        partTimePercentage: excelData.partTimePercentage || 100
      }]
    },
    pensionInfo: {
      code: excelData.pensionCode || null,
      codeDescription: excelData.codeDescription || '',
      codeEffectiveDate: excelData.codeEffectiveDate || '',
      pensionBase: null
    }
  }
}
